import { useEffect, useState } from 'react'
import { <PERSON>, Typo<PERSON>, But<PERSON>, Card, CardContent } from '@mui/material'
import MainCard from '@/ui-component/cards/MainCard'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import tokenUsageApi from '@/api/tokenUsage'
import chatflowsApi from '@/api/chatflows'

const TokenUsageTest = () => {
    const [testResults, setTestResults] = useState([])
    const [isLoading, setIsLoading] = useState(false)

    const addResult = (test, success, data, error) => {
        setTestResults(prev => [...prev, {
            test,
            success,
            data,
            error,
            timestamp: new Date().toLocaleTimeString()
        }])
    }

    const runTests = async () => {
        setIsLoading(true)
        setTestResults([])

        // Test 1: Chatflows API
        try {
            console.log('Testing chatflows API...')
            const chatflowsResponse = await chatflowsApi.getAllChatflows()
            addResult('Chatflows API', true, chatflowsResponse.data, null)
        } catch (error) {
            addResult('Chatflows API', false, null, error.message)
        }

        // Test 2: Token Usage API
        try {
            console.log('Testing token usage API...')
            const tokenResponse = await tokenUsageApi.getTokenUsageStats({})
            addResult('Token Usage API', true, tokenResponse.data, null)
        } catch (error) {
            addResult('Token Usage API', false, null, error.message)
        }

        // Test 3: Token Usage API with params
        try {
            console.log('Testing token usage API with params...')
            const params = {
                startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                endDate: new Date().toISOString()
            }
            const paramResponse = await tokenUsageApi.getTokenUsageStats(params)
            addResult('Token Usage API (with params)', true, paramResponse.data, null)
        } catch (error) {
            addResult('Token Usage API (with params)', false, null, error.message)
        }

        setIsLoading(false)
    }

    useEffect(() => {
        runTests()
    }, [])

    return (
        <MainCard>
            <ViewHeader
                title="Token Usage API Test"
                subtitle="Testing API endpoints for debugging"
            />
            
            <Box sx={{ mb: 3 }}>
                <Button 
                    variant="contained" 
                    onClick={runTests} 
                    disabled={isLoading}
                >
                    {isLoading ? 'Running Tests...' : 'Run Tests'}
                </Button>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {testResults.map((result, index) => (
                    <Card key={index} sx={{ 
                        backgroundColor: result.success ? 'success.light' : 'error.light',
                        color: result.success ? 'success.contrastText' : 'error.contrastText'
                    }}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                {result.success ? '✅' : '❌'} {result.test}
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                                Time: {result.timestamp}
                            </Typography>
                            {result.success ? (
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                        Success! Data received:
                                    </Typography>
                                    <pre style={{ 
                                        fontSize: '12px', 
                                        maxHeight: '200px', 
                                        overflow: 'auto',
                                        backgroundColor: 'rgba(0,0,0,0.1)',
                                        padding: '8px',
                                        borderRadius: '4px',
                                        marginTop: '8px'
                                    }}>
                                        {JSON.stringify(result.data, null, 2)}
                                    </pre>
                                </Box>
                            ) : (
                                <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                        Error:
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                        {result.error}
                                    </Typography>
                                </Box>
                            )}
                        </CardContent>
                    </Card>
                ))}
            </Box>

            {testResults.length === 0 && !isLoading && (
                <Typography variant="body1" color="textSecondary">
                    Click "Run Tests" to start testing the APIs
                </Typography>
            )}
        </MainCard>
    )
}

export default TokenUsageTest
