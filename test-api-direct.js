// Direct API test script
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testAPIs() {
    console.log('🧪 Testing APIs directly...\n');

    try {
        // Test 1: Check if server is running
        console.log('1. Testing server health...');
        const healthResponse = await axios.get(`${BASE_URL}/api/v1/chatflows`);
        console.log('✅ Server is running, chatflows API works');
        console.log('Chatflows count:', healthResponse.data.length);

        // Test 2: Test token-usage API
        console.log('\n2. Testing token-usage API...');
        try {
            const tokenResponse = await axios.get(`${BASE_URL}/api/v1/token-usage`);
            console.log('✅ Token usage API works');
            console.log('Response:', tokenResponse.data);
        } catch (tokenError) {
            console.log('❌ Token usage API failed:', tokenError.response?.status, tokenError.response?.statusText);
            console.log('Error details:', tokenError.response?.data);
        }

        // Test 3: Test with parameters
        console.log('\n3. Testing token-usage API with parameters...');
        try {
            const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
            const endDate = new Date().toISOString();
            const paramResponse = await axios.get(`${BASE_URL}/api/v1/token-usage?startDate=${startDate}&endDate=${endDate}`);
            console.log('✅ Token usage API with params works');
            console.log('Response:', paramResponse.data);
        } catch (paramError) {
            console.log('❌ Token usage API with params failed:', paramError.response?.status, paramError.response?.statusText);
            console.log('Error details:', paramError.response?.data);
        }

    } catch (error) {
        console.error('❌ Server test failed:', error.message);
        console.log('\n💡 Make sure Flowise server is running on http://localhost:3000');
        console.log('💡 Try running: pnpm start');
    }
}

testAPIs();
