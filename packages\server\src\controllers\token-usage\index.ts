import { Request, Response, NextFunction } from 'express'
import { getTokenUsageStats } from '../../services/token-usage'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'

const getTokenUsage = async (req: Request, res: Response, next: NextFunction) => {
    try {
        console.log('Token usage API called')
        const appServer = getRunningExpressApp()
        const { startDate, endDate, chatflowIds } = req.query

        // Parse chatflowIds if provided
        let parsedChatflowIds: string[] | undefined
        if (chatflowIds) {
            if (typeof chatflowIds === 'string') {
                parsedChatflowIds = chatflowIds.split(',').filter(id => id.trim())
            } else if (Array.isArray(chatflowIds)) {
                parsedChatflowIds = chatflowIds.filter(id => typeof id === 'string' && id.trim()) as string[]
            }
        }

        const stats = await getTokenUsageStats(
            appServer.AppDataSource,
            startDate as string,
            endDate as string,
            parsedChatflowIds
        )

        return res.json(stats)
    } catch (error) {
        console.error('Error in getTokenUsage controller:', error)
        // Return a default response instead of throwing error
        const defaultResponse = {
            overall: {
                totalCost: 0,
                totalTokens: 0,
                totalMessages: 0,
                promptTokens: 0,
                completionTokens: 0,
                averageCostPerMessage: 0,
                averageTokensPerMessage: 0
            },
            byFlow: [],
            byDate: [],
            byModel: []
        }
        return res.json(defaultResponse)
    }
}

export default {
    getTokenUsage
}
