import { Request, Response, NextFunction } from 'express'
import { getTokenUsageStats } from '../../services/token-usage'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'

const getTokenUsage = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const appServer = getRunningExpressApp()
        const { startDate, endDate, chatflowIds } = req.query

        // Parse chatflowIds if provided
        let parsedChatflowIds: string[] | undefined
        if (chatflowIds) {
            if (typeof chatflowIds === 'string') {
                parsedChatflowIds = chatflowIds.split(',').filter(id => id.trim())
            } else if (Array.isArray(chatflowIds)) {
                parsedChatflowIds = chatflowIds.filter(id => typeof id === 'string' && id.trim()) as string[]
            }
        }

        const stats = await getTokenUsageStats(
            appServer.AppDataSource,
            startDate as string,
            endDate as string,
            parsedChatflowIds
        )

        return res.json(stats)
    } catch (error) {
        next(error)
    }
}

export default {
    getTokenUsage
}
