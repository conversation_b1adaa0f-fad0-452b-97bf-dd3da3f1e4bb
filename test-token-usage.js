// Simple test script to verify token usage API functionality
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testTokenUsageAPI() {
    try {
        console.log('🧪 Testing Token Usage API...\n');

        // Test 1: Basic API endpoint
        console.log('1. Testing basic API endpoint...');
        const response = await fetch(`${BASE_URL}/api/v1/token-usage`);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API endpoint is accessible');
            console.log('📊 Response structure:', {
                hasOverall: !!data.overall,
                hasByFlow: Array.isArray(data.byFlow),
                hasByDate: Array.isArray(data.byDate),
                hasByModel: Array.isArray(data.byModel)
            });
            
            if (data.overall) {
                console.log('📈 Overall stats:', {
                    totalCost: data.overall.totalCost,
                    totalTokens: data.overall.totalTokens,
                    totalMessages: data.overall.totalMessages
                });
            }
        } else {
            console.log('❌ API endpoint failed:', response.status, response.statusText);
        }

        // Test 2: API with date range
        console.log('\n2. Testing API with date range...');
        const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
        const endDate = new Date().toISOString();
        
        const dateResponse = await fetch(`${BASE_URL}/api/v1/token-usage?startDate=${startDate}&endDate=${endDate}`);
        
        if (dateResponse.ok) {
            const dateData = await dateResponse.json();
            console.log('✅ Date range filtering works');
            console.log('📅 Date range data:', {
                dateEntries: dateData.byDate.length,
                flowEntries: dateData.byFlow.length
            });
        } else {
            console.log('❌ Date range filtering failed:', dateResponse.status);
        }

        console.log('\n🎉 Token Usage API tests completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n💡 Make sure Flowise server is running on http://localhost:3000');
    }
}

// Run the test
testTokenUsageAPI();
