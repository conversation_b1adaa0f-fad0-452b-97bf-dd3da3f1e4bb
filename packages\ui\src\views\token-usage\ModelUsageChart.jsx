import { useTheme } from '@mui/material/styles'
import { Card, CardContent, Typography, Box } from '@mui/material'
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Cell,
    ResponsiveContainer,
    Toolt<PERSON>,
    Legend
} from 'recharts'

const ModelUsageChart = ({ data }) => {
    const theme = useTheme()

    const COLORS = [
        theme.palette.primary.main,
        theme.palette.secondary.main,
        theme.palette.success.main,
        theme.palette.warning.main,
        theme.palette.error.main,
        theme.palette.info.main,
        '#8884d8',
        '#82ca9d',
        '#ffc658',
        '#ff7300'
    ]

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 4
        }).format(value)
    }

    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const CustomTooltip = ({ active, payload }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload
            return (
                <Box
                    sx={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1,
                        p: 1,
                        boxShadow: theme.shadows[3]
                    }}
                >
                    <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {data.model}
                    </Typography>
                    <Typography variant="body2">
                        Tokens: {formatNumber(data.stats.totalTokens)}
                    </Typography>
                    <Typography variant="body2">
                        Cost: {formatCurrency(data.stats.totalCost)}
                    </Typography>
                    <Typography variant="body2">
                        Messages: {formatNumber(data.stats.totalMessages)}
                    </Typography>
                </Box>
            )
        }
        return null
    }

    const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
        if (percent < 0.05) return null // Don't show labels for slices smaller than 5%
        
        const RADIAN = Math.PI / 180
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5
        const x = cx + radius * Math.cos(-midAngle * RADIAN)
        const y = cy + radius * Math.sin(-midAngle * RADIAN)

        return (
            <text
                x={x}
                y={y}
                fill="white"
                textAnchor={x > cx ? 'start' : 'end'}
                dominantBaseline="central"
                fontSize={12}
                fontWeight="bold"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        )
    }

    if (!data || data.length === 0) {
        return (
            <Card>
                <CardContent>
                    <Typography variant="h6" gutterBottom>
                        Usage by Model
                    </Typography>
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body2" color="textSecondary">
                            No model data available
                        </Typography>
                    </Box>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    Usage by Model
                </Typography>
                <Box sx={{ width: '100%', height: 400 }}>
                    <ResponsiveContainer>
                        <PieChart>
                            <Pie
                                data={data}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={renderCustomizedLabel}
                                outerRadius={120}
                                fill="#8884d8"
                                dataKey="stats.totalCost"
                            >
                                {data.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                            </Pie>
                            <Tooltip content={<CustomTooltip />} />
                            <Legend 
                                verticalAlign="bottom" 
                                height={36}
                                formatter={(value, entry) => entry.payload.model}
                            />
                        </PieChart>
                    </ResponsiveContainer>
                </Box>
            </CardContent>
        </Card>
    )
}

export default ModelUsageChart
