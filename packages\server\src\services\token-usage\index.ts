import { DataSource, Between, In } from 'typeorm'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { ChatFlow } from '../../database/entities/ChatFlow'
import { getErrorMessage } from '../../errors/utils'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { StatusCodes } from 'http-status-codes'

export interface TokenUsageStats {
    totalTokens: number
    totalCost: number
    totalMessages: number
    promptTokens: number
    completionTokens: number
    averageCostPerMessage: number
    averageTokensPerMessage: number
}

export interface FlowTokenUsage {
    chatflowId: string
    chatflowName: string
    stats: TokenUsageStats
}

export interface TokenUsageByDate {
    date: string
    stats: TokenUsageStats
}

export interface TokenUsageByModel {
    model: string
    stats: TokenUsageStats
}

export interface TokenUsageResponse {
    overall: TokenUsageStats
    byFlow: FlowTokenUsage[]
    byDate: TokenUsageByDate[]
    byModel: TokenUsageByModel[]
}

// Get token usage statistics
export const getTokenUsageStats = async (
    appDataSource: DataSource,
    startDate?: string,
    endDate?: string,
    chatflowIds?: string[],
    orgId?: string
): Promise<TokenUsageResponse> => {
    try {
        if (!appDataSource) {
            throw new Error('Database connection not available')
        }

        const chatMessageRepository = appDataSource.getRepository(ChatMessage)
        const chatFlowRepository = appDataSource.getRepository(ChatFlow)

        // Build query conditions
        const whereConditions: any = {}

        if (startDate && endDate) {
            whereConditions.createdDate = Between(new Date(startDate), new Date(endDate))
        }

        if (chatflowIds && chatflowIds.length > 0) {
            whereConditions.chatflowid = In(chatflowIds)
        }

        // Get all messages (we'll filter for token usage data later)
        const messages = await chatMessageRepository.find({
            where: whereConditions
        })

        // Filter messages that have token usage data
        const messagesWithTokens = messages.filter((msg) => msg.totalTokens != null && msg.totalTokens > 0)

        // Calculate overall stats
        const overall = calculateStats(messagesWithTokens)

        // Calculate stats by flow
        const flowGroups = groupBy(messagesWithTokens, 'chatflowid')
        const byFlow: FlowTokenUsage[] = []

        for (const [chatflowId, flowMessages] of Object.entries(flowGroups)) {
            const chatflow = await chatFlowRepository.findOne({ where: { id: chatflowId } })
            byFlow.push({
                chatflowId,
                chatflowName: chatflow?.name || 'Unknown Flow',
                stats: calculateStats(flowMessages)
            })
        }

        // Calculate stats by date (daily)
        const dateGroups = groupBy(messagesWithTokens, (msg) => msg.createdDate.toISOString().split('T')[0])
        const byDate: TokenUsageByDate[] = Object.entries(dateGroups)
            .map(([date, dateMessages]) => ({
                date,
                stats: calculateStats(dateMessages)
            }))
            .sort((a, b) => a.date.localeCompare(b.date))

        // Calculate stats by model
        const modelGroups = groupBy(
            messagesWithTokens.filter((msg) => msg.model),
            'model'
        )
        const byModel: TokenUsageByModel[] = Object.entries(modelGroups).map(([model, modelMessages]) => ({
            model,
            stats: calculateStats(modelMessages)
        }))

        return {
            overall,
            byFlow,
            byDate,
            byModel
        }
    } catch (error) {
        throw new InternalFlowiseError(
            StatusCodes.INTERNAL_SERVER_ERROR,
            `Error: tokenUsageService.getTokenUsageStats - ${getErrorMessage(error)}`
        )
    }
}

// Helper function to calculate statistics from messages
function calculateStats(messages: ChatMessage[]): TokenUsageStats {
    const totalMessages = messages.length
    const totalTokens = messages.reduce((sum, msg) => sum + (msg.totalTokens || 0), 0)
    const totalCost = messages.reduce((sum, msg) => sum + (msg.cost || 0), 0)
    const promptTokens = messages.reduce((sum, msg) => sum + (msg.promptTokens || 0), 0)
    const completionTokens = messages.reduce((sum, msg) => sum + (msg.completionTokens || 0), 0)

    return {
        totalTokens,
        totalCost: Number(totalCost.toFixed(6)),
        totalMessages,
        promptTokens,
        completionTokens,
        averageCostPerMessage: totalMessages > 0 ? Number((totalCost / totalMessages).toFixed(6)) : 0,
        averageTokensPerMessage: totalMessages > 0 ? Math.round(totalTokens / totalMessages) : 0
    }
}

// Helper function to group array by key
function groupBy<T>(array: T[], key: string | ((item: T) => string)): Record<string, T[]> {
    return array.reduce((groups, item) => {
        const groupKey = typeof key === 'function' ? key(item) : (item as any)[key]
        if (!groups[groupKey]) {
            groups[groupKey] = []
        }
        groups[groupKey].push(item)
        return groups
    }, {} as Record<string, T[]>)
}
