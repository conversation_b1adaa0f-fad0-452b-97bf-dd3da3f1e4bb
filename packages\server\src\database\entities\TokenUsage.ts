/* eslint-disable */
import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, PrimaryGeneratedColumn, Index } from 'typeorm'

export interface ITokenUsage {
    id: string
    chatflowId: string
    chatId: string
    executionId?: string
    nodeId: string
    nodeName: string
    model: string
    promptTokens: number
    completionTokens: number
    totalTokens: number
    cost: number
    createdDate: Date
    orgId?: string
    workspaceId?: string
}

@Entity()
export class TokenUsage implements ITokenUsage {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({ type: 'uuid' })
    chatflowId: string

    @Index()
    @Column({ type: 'varchar' })
    chatId: string

    @Column({ nullable: true, type: 'uuid' })
    executionId?: string

    @Column({ type: 'varchar' })
    nodeId: string

    @Column({ type: 'varchar' })
    nodeName: string

    @Column({ type: 'varchar' })
    model: string

    @Column({ type: 'integer' })
    promptTokens: number

    @Column({ type: 'integer' })
    completionTokens: number

    @Column({ type: 'integer' })
    totalTokens: number

    @Column({ type: 'decimal', precision: 10, scale: 6 })
    cost: number

    @Index()
    @CreateDateColumn()
    createdDate: Date

    @Column({ nullable: true, type: 'varchar' })
    orgId?: string

    @Column({ nullable: true, type: 'varchar' })
    workspaceId?: string
}
