import { useTheme } from '@mui/material/styles'
import { Card, CardContent, Typography, Box } from '@mui/material'
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer
} from 'recharts'

const TokenUsageChart = ({ data }) => {
    const theme = useTheme()

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 4
        }).format(value)
    }

    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <Box
                    sx={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1,
                        p: 1,
                        boxShadow: theme.shadows[3]
                    }}
                >
                    <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {label}
                    </Typography>
                    {payload.map((entry, index) => (
                        <Typography
                            key={index}
                            variant="body2"
                            sx={{ color: entry.color }}
                        >
                            {entry.name}: {entry.dataKey === 'totalCost' 
                                ? formatCurrency(entry.value) 
                                : formatNumber(entry.value)
                            }
                        </Typography>
                    ))}
                </Box>
            )
        }
        return null
    }

    return (
        <Card>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    Token Usage Trends
                </Typography>
                <Box sx={{ width: '100%', height: 400 }}>
                    <ResponsiveContainer>
                        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                            <XAxis 
                                dataKey="date" 
                                stroke={theme.palette.text.secondary}
                                fontSize={12}
                            />
                            <YAxis 
                                yAxisId="tokens"
                                orientation="left"
                                stroke={theme.palette.text.secondary}
                                fontSize={12}
                                tickFormatter={formatNumber}
                            />
                            <YAxis 
                                yAxisId="cost"
                                orientation="right"
                                stroke={theme.palette.text.secondary}
                                fontSize={12}
                                tickFormatter={formatCurrency}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            <Line
                                yAxisId="tokens"
                                type="monotone"
                                dataKey="stats.totalTokens"
                                stroke={theme.palette.primary.main}
                                strokeWidth={2}
                                dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 4 }}
                                name="Total Tokens"
                            />
                            <Line
                                yAxisId="cost"
                                type="monotone"
                                dataKey="stats.totalCost"
                                stroke={theme.palette.secondary.main}
                                strokeWidth={2}
                                dot={{ fill: theme.palette.secondary.main, strokeWidth: 2, r: 4 }}
                                name="Total Cost ($)"
                            />
                            <Line
                                yAxisId="tokens"
                                type="monotone"
                                dataKey="stats.totalMessages"
                                stroke={theme.palette.success.main}
                                strokeWidth={2}
                                dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 4 }}
                                name="Messages"
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </Box>
            </CardContent>
        </Card>
    )
}

export default TokenUsageChart
