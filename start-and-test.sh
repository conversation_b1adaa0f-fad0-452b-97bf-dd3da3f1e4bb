#!/bin/bash

echo "🚀 Starting Flowise with Token Usage Statistics Feature"
echo "=================================================="

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    pnpm install
fi

# Build the project
echo "🔨 Building project..."
pnpm build

# Start the server in background
echo "🌟 Starting Flowise server..."
pnpm start &
SERVER_PID=$!

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 10

# Test the API
echo "🧪 Testing Token Usage API..."
node test-token-usage.js

echo ""
echo "🎯 Token Usage Statistics Feature is ready!"
echo "📊 Access the statistics at: http://localhost:3000/token-usage"
echo "🔗 API endpoint: http://localhost:3000/api/v1/token-usage"
echo ""
echo "Press Ctrl+C to stop the server"

# Keep the script running
wait $SERVER_PID
