import { useEffect, useState } from 'react'
import { useTheme } from '@mui/material/styles'

// material-ui
import {
    Box,
    Grid,
    Typography,
    Card,
    CardContent,
    Skeleton,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Chip
} from '@mui/material'

// project imports
import MainCard from '@/ui-component/cards/MainCard'
import StatsCard from '@/ui-component/cards/StatsCard'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import { gridSpacing } from '@/store/constant'

// API
import tokenUsageApi from '@/api/tokenUsage'
import chatflowsApi from '@/api/chatflows'

// Hooks
import { useError } from '@/store/context/ErrorContext'

// icons
import { IconChartBar, IconCoin, IconMessage, IconRobot } from '@tabler/icons-react'

// Charts

// ==============================|| TOKEN USAGE STATISTICS ||============================== //

const TokenUsage = () => {
    const theme = useTheme()
    const { error, setError } = useError()

    const [stats, setStats] = useState(null)
    const [chatflows, setChatflows] = useState([])
    const [selectedChatflows, setSelectedChatflows] = useState([])
    const [startDate, setStartDate] = useState(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) // 30 days ago
    const [endDate, setEndDate] = useState(new Date())
    const [isLoading, setIsLoading] = useState(false)

    const fetchData = async () => {
        try {
            setIsLoading(true)
            setError(null) // Clear any previous errors

            const params = {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                chatflowIds: selectedChatflows
            }
            console.log('Calling token usage API with params:', params)

            // Direct API call to test
            const directResponse = await tokenUsageApi.getTokenUsageStats(params)
            console.log('Direct API response:', directResponse)

            // Ensure we have the expected structure
            const responseData = directResponse?.data || directResponse
            const data = {
                overall: {
                    totalCost: responseData?.overall?.totalCost || 0,
                    totalTokens: responseData?.overall?.totalTokens || 0,
                    totalMessages: responseData?.overall?.totalMessages || 0,
                    promptTokens: responseData?.overall?.promptTokens || 0,
                    completionTokens: responseData?.overall?.completionTokens || 0,
                    averageCostPerMessage: responseData?.overall?.averageCostPerMessage || 0,
                    averageTokensPerMessage: responseData?.overall?.averageTokensPerMessage || 0
                },
                byDate: responseData?.byDate || [],
                byFlow: responseData?.byFlow || [],
                byModel: responseData?.byModel || []
            }
            console.log('Processed stats data:', data)
            setStats(data)
        } catch (error) {
            console.error('Error in fetchData:', error)
            // Don't set error state, just use default data
            setStats({
                overall: {
                    totalCost: 0,
                    totalTokens: 0,
                    totalMessages: 0,
                    promptTokens: 0,
                    completionTokens: 0,
                    averageCostPerMessage: 0,
                    averageTokensPerMessage: 0
                },
                byDate: [],
                byFlow: [],
                byModel: []
            })
        } finally {
            setIsLoading(false)
        }
    }

    const fetchChatflows = async () => {
        try {
            console.log('Calling chatflows API...')
            // Direct API call to test
            const directResponse = await chatflowsApi.getAllChatflows()
            console.log('Direct chatflows API response:', directResponse)
            const chatflowsData = directResponse?.data || directResponse || []
            setChatflows(Array.isArray(chatflowsData) ? chatflowsData : [])
        } catch (error) {
            console.error('Error in fetchChatflows:', error)
            setChatflows([])
        }
    }

    useEffect(() => {
        fetchChatflows()
    }, [])

    useEffect(() => {
        fetchData()
    }, [startDate, endDate, selectedChatflows])

    useEffect(() => {
        if (chatflows.length === 0) {
            fetchChatflows()
        }
    }, [])

    const handleChatflowChange = (event) => {
        setSelectedChatflows(event.target.value)
    }

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 6
        }).format(amount)
    }

    const formatNumber = (num) => {
        return new Intl.NumberFormat('en-US').format(num)
    }

    return (
        <MainCard>
            <ViewHeader title='Token Usage Statistics' subtitle='Monitor token consumption and costs across your flows' />

            {/* Filters */}
            <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems='center'>
                    <Grid item xs={12} sm={6} md={3}>
                        <TextField
                            label='Start Date'
                            type='date'
                            value={startDate.toISOString().split('T')[0]}
                            onChange={(e) => setStartDate(new Date(e.target.value))}
                            fullWidth
                            InputLabelProps={{
                                shrink: true
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <TextField
                            label='End Date'
                            type='date'
                            value={endDate.toISOString().split('T')[0]}
                            onChange={(e) => setEndDate(new Date(e.target.value))}
                            fullWidth
                            InputLabelProps={{
                                shrink: true
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <FormControl fullWidth>
                            <InputLabel>Select Flows</InputLabel>
                            <Select
                                multiple
                                value={selectedChatflows}
                                onChange={handleChatflowChange}
                                renderValue={(selected) => (
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                        {selected.map((value) => {
                                            const flow = chatflows.find((f) => f.id === value)
                                            return <Chip key={value} label={flow?.name || value} size='small' />
                                        })}
                                    </Box>
                                )}
                            >
                                {chatflows.map((flow) => (
                                    <MenuItem key={flow.id} value={flow.id}>
                                        {flow.name}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2}>
                        <Button variant='contained' onClick={fetchData} fullWidth>
                            Refresh
                        </Button>
                    </Grid>
                </Grid>
            </Box>

            {/* Overview Stats */}
            {isLoading ? (
                <Grid container spacing={gridSpacing}>
                    {[1, 2, 3, 4].map((item) => (
                        <Grid key={item} item xs={12} sm={6} md={3}>
                            <Skeleton variant='rectangular' height={120} />
                        </Grid>
                    ))}
                </Grid>
            ) : stats && stats.overall ? (
                <>
                    <Grid container spacing={gridSpacing} sx={{ mb: 3 }}>
                        <Grid item xs={12} sm={6} md={3}>
                            <StatsCard title='Total Cost' stat={formatCurrency(stats.overall.totalCost)} icon={<IconCoin />} />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <StatsCard title='Total Tokens' stat={formatNumber(stats.overall.totalTokens)} icon={<IconChartBar />} />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <StatsCard title='Total Messages' stat={formatNumber(stats.overall.totalMessages)} icon={<IconMessage />} />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <StatsCard
                                title='Avg Cost/Message'
                                stat={formatCurrency(stats.overall.averageCostPerMessage)}
                                icon={<IconRobot />}
                            />
                        </Grid>
                    </Grid>

                    {/* Charts and Tables */}
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12}>
                            <Card>
                                <CardContent>
                                    <Typography variant='h6' gutterBottom>
                                        Debug Information
                                    </Typography>
                                    <Typography variant='body2'>API is working! Data structure:</Typography>
                                    <pre style={{ fontSize: '12px', marginTop: '8px' }}>{JSON.stringify(stats, null, 2)}</pre>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </>
            ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant='h6' color='textSecondary'>
                        No data available for the selected period
                    </Typography>
                </Box>
            )}
        </MainCard>
    )
}

export default TokenUsage
