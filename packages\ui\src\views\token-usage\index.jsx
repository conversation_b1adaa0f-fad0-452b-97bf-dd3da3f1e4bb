import { useEffect, useState } from 'react'
import { useTheme } from '@mui/material/styles'

// material-ui
import {
    Box,
    Grid,
    Typography,
    Card,
    CardContent,
    Skeleton,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Stack,
    Chip
} from '@mui/material'


// project imports
import MainCard from '@/ui-component/cards/MainCard'
import StatsCard from '@/ui-component/cards/StatsCard'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import ErrorBoundary from '@/ErrorBoundary'
import { gridSpacing } from '@/store/constant'

// API
import tokenUsageApi from '@/api/tokenUsage'
import chatflowsApi from '@/api/chatflows'

// Hooks
import useApi from '@/hooks/useApi'
import { useError } from '@/store/context/ErrorContext'

// icons
import { IconChartBar, IconCoin, IconMessage, IconRobot } from '@tabler/icons-react'

// Charts
import TokenUsageChart from './TokenUsageChart'
import FlowUsageTable from './FlowUsageTable'
import ModelUsageChart from './ModelUsageChart'

// ==============================|| TOKEN USAGE STATISTICS ||============================== //

const TokenUsage = () => {
    const theme = useTheme()
    const { error, setError } = useError()

    const [stats, setStats] = useState(null)
    const [chatflows, setChatflows] = useState([])
    const [selectedChatflows, setSelectedChatflows] = useState([])
    const [startDate, setStartDate] = useState(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) // 30 days ago
    const [endDate, setEndDate] = useState(new Date())

    const getTokenUsageApi = useApi(tokenUsageApi.getTokenUsageStats)
    const getAllChatflowsApi = useApi(chatflowsApi.getAllChatflows)

    const fetchData = async () => {
        const params = {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            chatflowIds: selectedChatflows
        }
        const response = await getTokenUsageApi.request(params)
        console.log('Token usage API response:', response)

        const data = response || {
            overall: {
                totalCost: 0,
                totalTokens: 0,
                totalMessages: 0,
                averageCostPerMessage: 0
            },
            byDate: [],
            byFlow: [],
            byModel: []
        }
        console.log('Processed stats data:', data)
        setStats(data)
    }

    const fetchChatflows = async () => {
        const response = await getAllChatflowsApi.request()
        console.log('Chatflows API response:', response)
        setChatflows(response || [])
    }

    useEffect(() => {
        fetchChatflows()
    }, [])

    useEffect(() => {
        fetchData()
    }, [startDate, endDate, selectedChatflows])

    useEffect(() => {
        if (chatflows.length === 0) {
            fetchChatflows()
        }
    }, [])

    const handleChatflowChange = (event) => {
        setSelectedChatflows(event.target.value)
    }

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 6
        }).format(amount)
    }

    const formatNumber = (num) => {
        return new Intl.NumberFormat('en-US').format(num)
    }

    return (
        <ErrorBoundary>
            <MainCard>
                <ViewHeader
                    title="Token Usage Statistics"
                    subtitle="Monitor token consumption and costs across your flows"
                />
                
                {/* Filters */}
                <Box sx={{ mb: 3 }}>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={6} md={3}>
                            <TextField
                                label="Start Date"
                                type="date"
                                value={startDate.toISOString().split('T')[0]}
                                onChange={(e) => setStartDate(new Date(e.target.value))}
                                fullWidth
                                InputLabelProps={{
                                    shrink: true,
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <TextField
                                label="End Date"
                                type="date"
                                value={endDate.toISOString().split('T')[0]}
                                onChange={(e) => setEndDate(new Date(e.target.value))}
                                fullWidth
                                InputLabelProps={{
                                    shrink: true,
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <FormControl fullWidth>
                                <InputLabel>Select Flows</InputLabel>
                                <Select
                                    multiple
                                    value={selectedChatflows}
                                    onChange={handleChatflowChange}
                                    renderValue={(selected) => (
                                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                            {selected.map((value) => {
                                                const flow = chatflows.find(f => f.id === value)
                                                return <Chip key={value} label={flow?.name || value} size="small" />
                                            })}
                                        </Box>
                                    )}
                                >
                                    {chatflows.map((flow) => (
                                        <MenuItem key={flow.id} value={flow.id}>
                                            {flow.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={2}>
                            <Button variant="contained" onClick={fetchData} fullWidth>
                                Refresh
                            </Button>
                        </Grid>
                    </Grid>
                </Box>

                {/* Overview Stats */}
                {(getTokenUsageApi.loading || getAllChatflowsApi.loading) ? (
                    <Grid container spacing={gridSpacing}>
                        {[1, 2, 3, 4].map((item) => (
                            <Grid key={item} item xs={12} sm={6} md={3}>
                                <Skeleton variant="rectangular" height={120} />
                            </Grid>
                        ))}
                    </Grid>
                ) : stats && stats.overall ? (
                    <>
                        <Grid container spacing={gridSpacing} sx={{ mb: 3 }}>
                            <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                    title="Total Cost"
                                    stat={formatCurrency(stats.overall.totalCost)}
                                    icon={<IconCoin />}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                    title="Total Tokens"
                                    stat={formatNumber(stats.overall.totalTokens)}
                                    icon={<IconChartBar />}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                    title="Total Messages"
                                    stat={formatNumber(stats.overall.totalMessages)}
                                    icon={<IconMessage />}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                    title="Avg Cost/Message"
                                    stat={formatCurrency(stats.overall.averageCostPerMessage)}
                                    icon={<IconRobot />}
                                />
                            </Grid>
                        </Grid>

                        {/* Charts and Tables */}
                        <Grid container spacing={gridSpacing}>
                            <Grid item xs={12} lg={8}>
                                <TokenUsageChart data={stats.byDate || []} />
                            </Grid>
                            <Grid item xs={12} lg={4}>
                                <ModelUsageChart data={stats.byModel || []} />
                            </Grid>
                            <Grid item xs={12}>
                                <FlowUsageTable data={stats.byFlow || []} />
                            </Grid>
                        </Grid>
                    </>
                ) : (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="h6" color="textSecondary">
                            No data available for the selected period
                        </Typography>
                    </Box>
                )}
            </MainCard>
        </ErrorBoundary>
    )
}

export default TokenUsage
