import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTokenUsageTable1748450240000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS "token_usage" (
                "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                "chatflowId" uuid NOT NULL,
                "chatId" varchar NOT NULL,
                "executionId" uuid,
                "nodeId" varchar NOT NULL,
                "nodeName" varchar NOT NULL,
                "model" varchar NOT NULL,
                "promptTokens" integer NOT NULL,
                "completionTokens" integer NOT NULL,
                "totalTokens" integer NOT NULL,
                "cost" decimal(10,6) NOT NULL,
                "createdDate" timestamp DEFAULT CURRENT_TIMESTAMP,
                "orgId" varchar,
                "workspaceId" varchar
            );
        `)

        // Create indexes for better query performance
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_chatflowId" ON "token_usage" ("chatflowId");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_chatId" ON "token_usage" ("chatId");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_createdDate" ON "token_usage" ("createdDate");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_model" ON "token_usage" ("model");`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "token_usage";`)
    }
}
