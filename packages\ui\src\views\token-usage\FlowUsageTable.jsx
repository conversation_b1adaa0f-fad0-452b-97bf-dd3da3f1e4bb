import { useState } from 'react'
import { useTheme } from '@mui/material/styles'
import {
    Card,
    CardContent,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TableSortLabel,
    Box,
    Chip
} from '@mui/material'

const FlowUsageTable = ({ data }) => {
    const theme = useTheme()
    const [order, setOrder] = useState('desc')
    const [orderBy, setOrderBy] = useState('totalCost')

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 6
        }).format(value)
    }

    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value)
    }

    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc'
        setOrder(isAsc ? 'desc' : 'asc')
        setOrderBy(property)
    }

    const sortedData = [...(data || [])].sort((a, b) => {
        let aValue, bValue
        
        switch (orderBy) {
            case 'flowName':
                aValue = a.chatflowName.toLowerCase()
                bValue = b.chatflowName.toLowerCase()
                break
            case 'totalCost':
                aValue = a.stats.totalCost
                bValue = b.stats.totalCost
                break
            case 'totalTokens':
                aValue = a.stats.totalTokens
                bValue = b.stats.totalTokens
                break
            case 'totalMessages':
                aValue = a.stats.totalMessages
                bValue = b.stats.totalMessages
                break
            case 'averageCostPerMessage':
                aValue = a.stats.averageCostPerMessage
                bValue = b.stats.averageCostPerMessage
                break
            default:
                aValue = a.stats.totalCost
                bValue = b.stats.totalCost
        }

        if (typeof aValue === 'string') {
            return order === 'asc' 
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue)
        }

        return order === 'asc' ? aValue - bValue : bValue - aValue
    })

    const getCostColor = (cost, maxCost) => {
        const ratio = cost / maxCost
        if (ratio > 0.7) return theme.palette.error.main
        if (ratio > 0.4) return theme.palette.warning.main
        return theme.palette.success.main
    }

    const maxCost = Math.max(...(data || []).map(item => item.stats.totalCost))

    if (!data || data.length === 0) {
        return (
            <Card>
                <CardContent>
                    <Typography variant="h6" gutterBottom>
                        Usage by Flow
                    </Typography>
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body2" color="textSecondary">
                            No flow data available
                        </Typography>
                    </Box>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    Usage by Flow
                </Typography>
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>
                                    <TableSortLabel
                                        active={orderBy === 'flowName'}
                                        direction={orderBy === 'flowName' ? order : 'asc'}
                                        onClick={() => handleRequestSort('flowName')}
                                    >
                                        Flow Name
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell align="right">
                                    <TableSortLabel
                                        active={orderBy === 'totalCost'}
                                        direction={orderBy === 'totalCost' ? order : 'asc'}
                                        onClick={() => handleRequestSort('totalCost')}
                                    >
                                        Total Cost
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell align="right">
                                    <TableSortLabel
                                        active={orderBy === 'totalTokens'}
                                        direction={orderBy === 'totalTokens' ? order : 'asc'}
                                        onClick={() => handleRequestSort('totalTokens')}
                                    >
                                        Total Tokens
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell align="right">
                                    <TableSortLabel
                                        active={orderBy === 'totalMessages'}
                                        direction={orderBy === 'totalMessages' ? order : 'asc'}
                                        onClick={() => handleRequestSort('totalMessages')}
                                    >
                                        Messages
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell align="right">
                                    <TableSortLabel
                                        active={orderBy === 'averageCostPerMessage'}
                                        direction={orderBy === 'averageCostPerMessage' ? order : 'asc'}
                                        onClick={() => handleRequestSort('averageCostPerMessage')}
                                    >
                                        Avg Cost/Message
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell align="center">Status</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {sortedData.map((row) => (
                                <TableRow key={row.chatflowId} hover>
                                    <TableCell>
                                        <Typography variant="body2" fontWeight="medium">
                                            {row.chatflowName}
                                        </Typography>
                                        <Typography variant="caption" color="textSecondary">
                                            {row.chatflowId}
                                        </Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                        <Typography 
                                            variant="body2" 
                                            fontWeight="medium"
                                            color={getCostColor(row.stats.totalCost, maxCost)}
                                        >
                                            {formatCurrency(row.stats.totalCost)}
                                        </Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                        {formatNumber(row.stats.totalTokens)}
                                    </TableCell>
                                    <TableCell align="right">
                                        {formatNumber(row.stats.totalMessages)}
                                    </TableCell>
                                    <TableCell align="right">
                                        {formatCurrency(row.stats.averageCostPerMessage)}
                                    </TableCell>
                                    <TableCell align="center">
                                        <Chip
                                            label={row.stats.totalCost > maxCost * 0.7 ? 'High Usage' : 
                                                   row.stats.totalCost > maxCost * 0.4 ? 'Medium Usage' : 'Low Usage'}
                                            color={row.stats.totalCost > maxCost * 0.7 ? 'error' : 
                                                   row.stats.totalCost > maxCost * 0.4 ? 'warning' : 'success'}
                                            size="small"
                                        />
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </CardContent>
        </Card>
    )
}

export default FlowUsageTable
