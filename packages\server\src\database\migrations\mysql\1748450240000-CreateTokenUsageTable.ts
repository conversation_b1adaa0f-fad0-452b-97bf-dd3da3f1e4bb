import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTokenUsageTable1748450240000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`token_usage\` (
                \`id\` varchar(36) PRIMARY KEY DEFAULT (UUID()),
                \`chatflowId\` varchar(36) NOT NULL,
                \`chatId\` varchar(255) NOT NULL,
                \`flowRunId\` varchar(36) NULL,
                \`nodeId\` varchar(255) NOT NULL,
                \`nodeName\` varchar(255) NOT NULL,
                \`model\` varchar(255) NOT NULL,
                \`promptTokens\` int NOT NULL,
                \`completionTokens\` int NOT NULL,
                \`totalTokens\` int NOT NULL,
                \`cost\` decimal(10,6) NOT NULL,
                \`createdDate\` timestamp DEFAULT CURRENT_TIMESTAMP,
                \`orgId\` varchar(255) NULL,
                \`workspaceId\` varchar(255) NULL
            );
        `)

        // Create indexes for better query performance
        await queryRunner.query(`CREATE INDEX \`IDX_token_usage_chatflowId\` ON \`token_usage\` (\`chatflowId\`);`)
        await queryRunner.query(`CREATE INDEX \`IDX_token_usage_chatId\` ON \`token_usage\` (\`chatId\`);`)
        await queryRunner.query(`CREATE INDEX \`IDX_token_usage_createdDate\` ON \`token_usage\` (\`createdDate\`);`)
        await queryRunner.query(`CREATE INDEX \`IDX_token_usage_model\` ON \`token_usage\` (\`model\`);`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS \`token_usage\`;`)
    }
}
