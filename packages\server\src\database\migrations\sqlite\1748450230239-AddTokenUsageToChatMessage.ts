import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTokenUsageToChatMessage1748450230239 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chat_message" ADD COLUMN "promptTokens" INTEGER;`)
        await queryRunner.query(`ALTER TABLE "chat_message" ADD COLUMN "completionTokens" INTEGER;`)
        await queryRunner.query(`ALTER TABLE "chat_message" ADD COLUMN "totalTokens" INTEGER;`)
        await queryRunner.query(`ALTER TABLE "chat_message" ADD COLUMN "cost" REAL;`)
        await queryRunner.query(`ALTER TABLE "chat_message" ADD COLUMN "model" TEXT;`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "promptTokens";`)
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "completionTokens";`)
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "totalTokens";`)
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "cost";`)
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "model";`)
    }
}
