# Token Usage Statistics Feature

## 概述

这个功能为 Flowise 添加了类似 OpenAI 计费统计界面的 Token 使用量统计功能，可以帮助用户监控各个流的 Token 消耗和成本。

## 功能特性

### 1. 数据库扩展
- 在 `ChatMessage` 实体中添加了以下字段：
  - `promptTokens`: 输入 Token 数量
  - `completionTokens`: 输出 Token 数量  
  - `totalTokens`: 总 Token 数量
  - `cost`: 成本（美元）
  - `model`: 使用的模型名称

### 2. 后端 API
- **路径**: `/api/v1/token-usage`
- **方法**: GET
- **查询参数**:
  - `startDate`: 开始日期 (ISO 字符串)
  - `endDate`: 结束日期 (ISO 字符串)
  - `chatflowIds`: 流 ID 列表（逗号分隔）

### 3. 前端界面
- **路径**: `/token-usage`
- **功能**:
  - 总体统计卡片（总成本、总 Token、总消息数、平均成本）
  - 时间趋势图表
  - 模型使用量饼图
  - 流使用量排行表格
  - 日期范围筛选
  - 流筛选

### 4. 自动 Token 追踪
- 在 LLM 调用完成后自动提取 Token 使用量信息
- 支持多种 LLM 提供商的响应格式
- 自动计算成本（基于内置的模型定价）

## 支持的模型定价

内置了以下模型的定价信息（每 1K Token）：

### OpenAI
- GPT-4: 输入 $0.03, 输出 $0.06
- GPT-4 Turbo: 输入 $0.01, 输出 $0.03
- GPT-3.5 Turbo: 输入 $0.0015, 输出 $0.002

### Anthropic Claude
- Claude-3 Opus: 输入 $0.015, 输出 $0.075
- Claude-3 Sonnet: 输入 $0.003, 输出 $0.015
- Claude-3 Haiku: 输入 $0.00025, 输出 $0.00125

### Google
- Gemini Pro: 输入 $0.0005, 输出 $0.0015

## 使用方法

### 1. 数据库迁移
系统启动时会自动运行数据库迁移，为 `chat_message` 表添加新字段。

### 2. 访问统计界面
1. 登录 Flowise 管理界面
2. 在左侧导航菜单中点击 "Token Usage"
3. 使用日期筛选器选择时间范围
4. 可选择特定的流进行筛选
5. 查看统计数据和图表

### 3. API 使用示例
```bash
# 获取最近 30 天的统计数据
curl "http://localhost:3000/api/v1/token-usage?startDate=2024-01-01&endDate=2024-01-31"

# 获取特定流的统计数据
curl "http://localhost:3000/api/v1/token-usage?chatflowIds=flow1,flow2"
```

## 技术实现

### 后端组件
1. **Token 追踪服务** (`packages/server/src/services/token-tracking/`)
   - 提取 Token 使用量信息
   - 计算成本
   - 模型名称标准化

2. **统计服务** (`packages/server/src/services/token-usage/`)
   - 聚合统计数据
   - 按日期、流、模型分组

3. **数据库迁移** (`packages/server/src/database/migrations/*/AddTokenUsageToChatMessage.ts`)
   - 支持 PostgreSQL、MySQL、MariaDB、SQLite

### 前端组件
1. **主页面** (`packages/ui/src/views/token-usage/index.jsx`)
2. **图表组件**:
   - `TokenUsageChart.jsx`: 时间趋势图
   - `ModelUsageChart.jsx`: 模型使用量饼图
   - `FlowUsageTable.jsx`: 流使用量表格

## 注意事项

1. **成本计算**: 基于内置定价表，实际成本可能因提供商定价变化而有所不同
2. **Token 提取**: 依赖于 LLM 响应中的 Token 使用量信息
3. **历史数据**: 只有在功能启用后的新对话才会有 Token 统计数据
4. **权限**: 需要 `chatflows:view` 权限才能访问统计界面

## 扩展和自定义

### 添加新模型定价
可以通过修改 `packages/server/src/services/token-tracking/index.ts` 中的 `MODEL_PRICING` 对象来添加新模型的定价。

### 自定义统计维度
可以扩展统计服务以支持更多维度的数据聚合，如按用户、按时间段等。

## 快速开始

### 1. 构建和启动
```bash
# 构建项目
pnpm build

# 启动服务器
pnpm start
```

### 2. 使用测试脚本
我们提供了测试脚本来验证功能：

**Linux/Mac:**
```bash
chmod +x start-and-test.sh
./start-and-test.sh
```

**Windows:**
```cmd
start-and-test.bat
```

**手动测试:**
```bash
node test-token-usage.js
```

### 3. 访问界面
- 统计界面: http://localhost:3000/token-usage
- API 端点: http://localhost:3000/api/v1/token-usage

## 故障排除

1. **编译错误**: 运行 `pnpm build` 检查编译错误
2. **统计数据为空**: 确保有新的对话数据，并且 LLM 响应包含 Token 使用量信息
3. **成本显示为 0**: 检查模型名称是否在定价表中，或者 LLM 响应是否包含正确的 Token 数据
4. **图表不显示**: 检查浏览器控制台是否有 JavaScript 错误，确保所有依赖已正确加载
5. **API 404 错误**: 确保服务器已启动并且路由已正确注册
