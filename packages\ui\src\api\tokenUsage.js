import client from './client'

const getTokenUsageStats = (params) => {
    const queryParams = new URLSearchParams()
    
    if (params?.startDate) queryParams.append('startDate', params.startDate)
    if (params?.endDate) queryParams.append('endDate', params.endDate)
    if (params?.chatflowIds && params.chatflowIds.length > 0) {
        queryParams.append('chatflowIds', params.chatflowIds.join(','))
    }
    
    const queryString = queryParams.toString()
    const url = queryString ? `/token-usage?${queryString}` : '/token-usage'
    
    return client.get(url)
}

const tokenUsageApi = {
    getTokenUsageStats
}

export default tokenUsageApi
