import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTokenUsageTable1748450240000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS "token_usage" (
                "id" TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
                "chatflowId" TEXT NOT NULL,
                "chatId" TEXT NOT NULL,
                "flowRunId" TEXT,
                "nodeId" TEXT NOT NULL,
                "nodeName" TEXT NOT NULL,
                "model" TEXT NOT NULL,
                "promptTokens" INTEGER NOT NULL,
                "completionTokens" INTEGER NOT NULL,
                "totalTokens" INTEGER NOT NULL,
                "cost" REAL NOT NULL,
                "createdDate" DATETIME DEFAULT CURRENT_TIMESTAMP,
                "orgId" TEXT,
                "workspaceId" TEXT
            );
        `)

        // Create indexes for better query performance
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_chatflowId" ON "token_usage" ("chatflowId");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_chatId" ON "token_usage" ("chatId");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_createdDate" ON "token_usage" ("createdDate");`)
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_token_usage_model" ON "token_usage" ("model");`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS "token_usage";`)
    }
}
