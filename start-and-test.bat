@echo off
echo 🚀 Starting Flowise with Token Usage Statistics Feature
echo ==================================================

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    call pnpm install
)

REM Build the project
echo 🔨 Building project...
call pnpm build

REM Start the server
echo 🌟 Starting Flowise server...
start /B pnpm start

REM Wait for server to start
echo ⏳ Waiting for server to start...
timeout /t 10 /nobreak > nul

REM Test the API
echo 🧪 Testing Token Usage API...
node test-token-usage.js

echo.
echo 🎯 Token Usage Statistics Feature is ready!
echo 📊 Access the statistics at: http://localhost:3000/token-usage
echo 🔗 API endpoint: http://localhost:3000/api/v1/token-usage
echo.
echo Press any key to exit...
pause > nul
