import PropTypes from 'prop-types'

import { useSelector } from 'react-redux'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Box } from '@mui/material'

const StatsCard = ({ title, stat, icon }) => {
    const customization = useSelector((state) => state.customization)
    return (
        <Card sx={{ border: '1px solid #e0e0e0', borderRadius: `${customization.borderRadius}px` }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography sx={{ fontSize: '0.875rem' }} color='text.primary' gutterBottom>
                        {title}
                    </Typography>
                    {icon && (
                        <Box sx={{ color: 'primary.main' }}>
                            {icon}
                        </Box>
                    )}
                </Box>
                <Typography sx={{ fontSize: '1.5rem', fontWeight: 500 }} color='text.primary'>
                    {stat}
                </Typography>
            </CardContent>
        </Card>
    )
}

StatsCard.propTypes = {
    title: PropTypes.string,
    stat: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    icon: PropTypes.node
}

export default StatsCard
