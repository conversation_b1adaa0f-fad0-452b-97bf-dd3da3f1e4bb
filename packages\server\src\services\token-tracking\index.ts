// Default fallback pricing (per 1K tokens)
const DEFAULT_PRICING = { input: 0.001, output: 0.002 }

export interface TokenUsageData {
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    model?: string
    cost?: number
}

/**
 * Calculate the cost of token usage based on model and token counts
 * Note: Currently uses default pricing. In the future, this could be enhanced
 * to fetch actual pricing from models.json based on the model name.
 */
export const calculateTokenCost = (promptTokens: number = 0, completionTokens: number = 0, _model: string = 'default'): number => {
    // For now, use default pricing for all models
    // TODO: Implement dynamic pricing lookup from models.json
    const pricing = DEFAULT_PRICING

    const promptCost = (promptTokens / 1000) * pricing.input
    const completionCost = (completionTokens / 1000) * pricing.output

    return Number((promptCost + completionCost).toFixed(6))
}

/**
 * Extract token usage data from LLM response
 */
export const extractTokenUsage = (llmOutput: any, model?: string): TokenUsageData | null => {
    if (!llmOutput) {
        return null
    }

    let tokenUsage: any = null
    let extractedModel = model

    // Try to extract token usage from different response formats
    if (llmOutput.estimatedTokenUsage) {
        tokenUsage = llmOutput.estimatedTokenUsage
    } else if (llmOutput.tokenUsage) {
        tokenUsage = llmOutput.tokenUsage
    } else if (llmOutput.llmOutput?.estimatedTokenUsage) {
        tokenUsage = llmOutput.llmOutput.estimatedTokenUsage
    } else if (llmOutput.llmOutput?.tokenUsage) {
        tokenUsage = llmOutput.llmOutput.tokenUsage
    } else if (llmOutput.usage_metadata) {
        // LangChain usage_metadata format
        tokenUsage = {
            promptTokens: llmOutput.usage_metadata.input_tokens || llmOutput.usage_metadata.prompt_tokens,
            completionTokens: llmOutput.usage_metadata.output_tokens || llmOutput.usage_metadata.completion_tokens,
            totalTokens: llmOutput.usage_metadata.total_tokens
        }
    } else if (llmOutput.usage) {
        // Anthropic/OpenAI format
        tokenUsage = {
            promptTokens: llmOutput.usage.input_tokens || llmOutput.usage.prompt_tokens,
            completionTokens: llmOutput.usage.output_tokens || llmOutput.usage.completion_tokens,
            totalTokens: llmOutput.usage.total_tokens
        }
    } else if (llmOutput.response?.usage) {
        tokenUsage = {
            promptTokens: llmOutput.response.usage.input_tokens || llmOutput.response.usage.prompt_tokens,
            completionTokens: llmOutput.response.usage.output_tokens || llmOutput.response.usage.completion_tokens,
            totalTokens: llmOutput.response.usage.total_tokens
        }
    }

    if (!tokenUsage) {
        return null
    }

    // Extract model name if not provided
    if (!extractedModel) {
        extractedModel = llmOutput.model || llmOutput.llmOutput?.model || llmOutput.response?.model || 'default'
    }

    const promptTokens = tokenUsage.promptTokens || 0
    const completionTokens = tokenUsage.completionTokens || 0
    const totalTokens = tokenUsage.totalTokens || promptTokens + completionTokens

    const cost = calculateTokenCost(promptTokens, completionTokens, extractedModel)

    return {
        promptTokens,
        completionTokens,
        totalTokens,
        model: extractedModel,
        cost
    }
}
