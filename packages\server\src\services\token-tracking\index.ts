// Model pricing data (per 1K tokens)
// These are approximate prices and should be updated based on actual provider pricing
const MODEL_PRICING: Record<string, { input: number; output: number }> = {
    // OpenAI GPT models
    'gpt-4': { input: 0.03, output: 0.06 },
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
    'gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
    'gpt-4-0125-preview': { input: 0.01, output: 0.03 },
    'gpt-4-1106-preview': { input: 0.01, output: 0.03 },
    'gpt-4-vision-preview': { input: 0.01, output: 0.03 },
    'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    'gpt-3.5-turbo-0125': { input: 0.0005, output: 0.0015 },
    'gpt-3.5-turbo-instruct': { input: 0.0015, output: 0.002 },

    // Anthropic Claude models
    'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
    'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
    'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
    'claude-2.1': { input: 0.008, output: 0.024 },
    'claude-2.0': { input: 0.008, output: 0.024 },
    'claude-instant-1.2': { input: 0.0008, output: 0.0024 },

    // Google models
    'gemini-pro': { input: 0.0005, output: 0.0015 },
    'gemini-pro-vision': { input: 0.0005, output: 0.0015 },

    // Default fallback pricing
    default: { input: 0.001, output: 0.002 }
}

export interface TokenUsageData {
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
    model?: string
    cost?: number
}

/**
 * Calculate the cost of token usage based on model and token counts
 */
export const calculateTokenCost = (promptTokens: number = 0, completionTokens: number = 0, model: string = 'default'): number => {
    // Normalize model name to match pricing keys
    const normalizedModel = normalizeModelName(model)
    const pricing = MODEL_PRICING[normalizedModel] || MODEL_PRICING['default']

    const promptCost = (promptTokens / 1000) * pricing.input
    const completionCost = (completionTokens / 1000) * pricing.output

    return Number((promptCost + completionCost).toFixed(6))
}

/**
 * Extract token usage data from LLM response
 */
export const extractTokenUsage = (llmOutput: any, model?: string): TokenUsageData | null => {
    console.log('🔍 [Token Tracker] Starting token extraction:', {
        hasLlmOutput: !!llmOutput,
        llmOutputType: typeof llmOutput,
        model,
        llmOutputKeys: llmOutput ? Object.keys(llmOutput) : []
    })

    if (!llmOutput) {
        console.log('❌ [Token Tracker] No LLM output provided')
        return null
    }

    let tokenUsage: any = null
    let extractedModel = model

    // Try to extract token usage from different response formats
    if (llmOutput.estimatedTokenUsage) {
        console.log('✅ [Token Tracker] Found estimatedTokenUsage in llmOutput.estimatedTokenUsage')
        tokenUsage = llmOutput.estimatedTokenUsage
    } else if (llmOutput.tokenUsage) {
        console.log('✅ [Token Tracker] Found tokenUsage in llmOutput.tokenUsage')
        tokenUsage = llmOutput.tokenUsage
    } else if (llmOutput.llmOutput?.estimatedTokenUsage) {
        console.log('✅ [Token Tracker] Found estimatedTokenUsage in llmOutput.llmOutput.estimatedTokenUsage')
        tokenUsage = llmOutput.llmOutput.estimatedTokenUsage
    } else if (llmOutput.llmOutput?.tokenUsage) {
        console.log('✅ [Token Tracker] Found tokenUsage in llmOutput.llmOutput.tokenUsage')
        tokenUsage = llmOutput.llmOutput.tokenUsage
    } else if (llmOutput.usage_metadata) {
        console.log('✅ [Token Tracker] Found usage_metadata in llmOutput.usage_metadata (LangChain format)')
        // LangChain usage_metadata format
        tokenUsage = {
            promptTokens: llmOutput.usage_metadata.input_tokens || llmOutput.usage_metadata.prompt_tokens,
            completionTokens: llmOutput.usage_metadata.output_tokens || llmOutput.usage_metadata.completion_tokens,
            totalTokens: llmOutput.usage_metadata.total_tokens
        }
        console.log('🔍 [Token Tracker] Converted usage_metadata format:', tokenUsage)
    } else if (llmOutput.usage) {
        console.log('✅ [Token Tracker] Found usage in llmOutput.usage (Anthropic/OpenAI format)')
        // Anthropic/OpenAI format
        tokenUsage = {
            promptTokens: llmOutput.usage.input_tokens || llmOutput.usage.prompt_tokens,
            completionTokens: llmOutput.usage.output_tokens || llmOutput.usage.completion_tokens,
            totalTokens: llmOutput.usage.total_tokens
        }
        console.log('🔍 [Token Tracker] Converted usage format:', tokenUsage)
    } else if (llmOutput.response?.usage) {
        console.log('✅ [Token Tracker] Found usage in llmOutput.response.usage')
        tokenUsage = {
            promptTokens: llmOutput.response.usage.input_tokens || llmOutput.response.usage.prompt_tokens,
            completionTokens: llmOutput.response.usage.output_tokens || llmOutput.response.usage.completion_tokens,
            totalTokens: llmOutput.response.usage.total_tokens
        }
        console.log('🔍 [Token Tracker] Converted response.usage format:', tokenUsage)
    } else {
        console.log('❌ [Token Tracker] No token usage found in any expected format')
        console.log('🔍 [Token Tracker] Available properties:', Object.keys(llmOutput))
        console.log('🔍 [Token Tracker] Full llmOutput structure:', JSON.stringify(llmOutput, null, 2))
        if (llmOutput.llmOutput) {
            console.log('🔍 [Token Tracker] llmOutput properties:', Object.keys(llmOutput.llmOutput))
        }
        if (llmOutput.response) {
            console.log('🔍 [Token Tracker] response properties:', Object.keys(llmOutput.response))
        }
    }

    if (!tokenUsage) {
        console.log('❌ [Token Tracker] No token usage extracted')
        return null
    }

    // Extract model name if not provided
    if (!extractedModel) {
        extractedModel = llmOutput.model || llmOutput.llmOutput?.model || llmOutput.response?.model || 'default'
    }

    const promptTokens = tokenUsage.promptTokens || 0
    const completionTokens = tokenUsage.completionTokens || 0
    const totalTokens = tokenUsage.totalTokens || promptTokens + completionTokens

    const cost = calculateTokenCost(promptTokens, completionTokens, extractedModel)

    return {
        promptTokens,
        completionTokens,
        totalTokens,
        model: extractedModel,
        cost
    }
}

/**
 * Normalize model name to match pricing keys
 */
function normalizeModelName(model: string): string {
    if (!model) return 'default'

    const normalized = model.toLowerCase()

    // Check for exact matches first
    if (MODEL_PRICING[normalized]) {
        return normalized
    }

    // Check for partial matches
    for (const key of Object.keys(MODEL_PRICING)) {
        if (normalized.includes(key) || key.includes(normalized)) {
            return key
        }
    }

    // Special cases for common model variations
    if (normalized.includes('gpt-4')) {
        if (normalized.includes('turbo')) {
            return 'gpt-4-turbo'
        }
        return 'gpt-4'
    }

    if (normalized.includes('gpt-3.5')) {
        return 'gpt-3.5-turbo'
    }

    if (normalized.includes('claude-3')) {
        if (normalized.includes('opus')) return 'claude-3-opus-20240229'
        if (normalized.includes('sonnet')) return 'claude-3-sonnet-20240229'
        if (normalized.includes('haiku')) return 'claude-3-haiku-20240307'
    }

    if (normalized.includes('claude-2')) {
        return 'claude-2.1'
    }

    if (normalized.includes('gemini')) {
        return 'gemini-pro'
    }

    return 'default'
}

/**
 * Update model pricing (for admin configuration)
 */
export const updateModelPricing = (model: string, inputPrice: number, outputPrice: number): void => {
    MODEL_PRICING[model.toLowerCase()] = {
        input: inputPrice,
        output: outputPrice
    }
}

/**
 * Get all model pricing data
 */
export const getModelPricing = (): Record<string, { input: number; output: number }> => {
    return { ...MODEL_PRICING }
}
